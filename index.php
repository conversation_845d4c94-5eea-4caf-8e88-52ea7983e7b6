<?php
// SquidLink PHP 8.4.7 - Main Entry Point
// Converted from Node.js/React to PHP while maintaining exact design and functionality

session_start();

// Configuration
define('BASE_PATH', __DIR__);
define('ASSETS_PATH', '/assets');
define('CSS_PATH', '/css');
define('JS_PATH', '/js');

// Cache Management System
class SquidCacheManager {
    private $cacheVersion;
    private $cacheDir;

    public function __construct() {
        $this->cacheDir = BASE_PATH . '/cache';
        $this->ensureCacheDir();
        $this->cacheVersion = $this->getCurrentVersion();
    }

    private function ensureCacheDir() {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }

    public function getCurrentVersion() {
        $versionFile = $this->cacheDir . '/version.txt';
        if (file_exists($versionFile)) {
            return trim(file_get_contents($versionFile));
        }
        return $this->generateNewVersion();
    }

    public function generateNewVersion() {
        $version = 'v' . date('Y.m.d.His') . '_' . substr(md5(microtime()), 0, 8);
        $versionFile = $this->cacheDir . '/version.txt';
        file_put_contents($versionFile, $version);
        return $version;
    }

    public function getCacheBustParam() {
        return '?v=' . $this->cacheVersion;
    }

    public function clearCache() {
        // Clear browser cache by updating version
        $this->generateNewVersion();

        // Clear PHP session cache
        if (session_status() === PHP_SESSION_ACTIVE) {
            session_destroy();
            session_start();
        }

        // Log cache clear event
        $this->logCacheEvent('cache_cleared');

        return true;
    }

    public function restoreCache() {
        // Restore to previous version if available
        $backupFile = $this->cacheDir . '/version_backup.txt';
        if (file_exists($backupFile)) {
            $backupVersion = trim(file_get_contents($backupFile));
            file_put_contents($this->cacheDir . '/version.txt', $backupVersion);
            $this->cacheVersion = $backupVersion;
            $this->logCacheEvent('cache_restored', $backupVersion);
            return true;
        }
        return false;
    }

    public function backupCurrentVersion() {
        $backupFile = $this->cacheDir . '/version_backup.txt';
        file_put_contents($backupFile, $this->cacheVersion);
    }

    private function logCacheEvent($event, $data = null) {
        $logDir = BASE_PATH . '/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'version' => $this->cacheVersion,
            'data' => $data,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];

        $logFile = $logDir . '/cache_' . date('Y-m-d') . '.log';
        file_put_contents($logFile, json_encode($logEntry, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND | LOCK_EX);
    }
}

// Initialize cache manager
$cacheManager = new SquidCacheManager();

// Handle AJAX requests
if (isset($_GET['action'])) {
    header('Content-Type: application/json');

    switch ($_GET['action']) {
        case 'contact':
            include 'api/contact.php';
            break;
        case 'nfc':
            include 'api/nfc.php';
            break;
        case 'cache':
            include 'api/cache.php';
            break;
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
    }
    exit;
}

// Get current name for glitch animation
$names = ['FAHIM', 'HASAN', 'SANTO'];
$currentNameIndex = isset($_SESSION['name_index']) ? $_SESSION['name_index'] : 0;
$currentName = $names[$currentNameIndex];

// Check for admin mode
$adminMode = isset($_GET['admin']) && $_GET['admin'] === 'squid456';

// Projects data (same as React version)
$projects = [
    [
        'id' => 1,
        'name' => 'Jashore Sell Bazar',
        'description' => 'E-commerce platform for local marketplace',
        'url' => 'https://jashoresellbazar.com',
        'playerNumber' => '001',
        'color' => 'from-red-500 to-pink-600'
    ],
    [
        'id' => 2,
        'name' => 'Athlete Bazaar',
        'description' => 'Sports equipment marketplace',
        'url' => 'https://athletebazaar.com',
        'playerNumber' => '067',
        'color' => 'from-blue-500 to-cyan-600'
    ],
    [
        'id' => 3,
        'name' => 'Shadhinalo',
        'description' => 'Cultural platform for Bengali community',
        'url' => 'https://shadhinalo.com',
        'playerNumber' => '218',
        'color' => 'from-green-500 to-emerald-600'
    ]
];

// Player skills data
$playerSkills = [
    [
        'id' => 1,
        'name' => 'HTML CSS JS',
        'level' => 'EXPERT',
        'playerNumber' => '101',
        'color' => 'from-gray-900 to-black',
        'mastery' => 95
    ],
    [
        'id' => 2,
        'name' => 'WordPress',
        'level' => 'ADVANCED',
        'playerNumber' => '202',
        'color' => 'from-gray-900 to-black',
        'mastery' => 90
    ],
    [
        'id' => 3,
        'name' => 'Full Stack Web',
        'level' => 'MASTER',
        'playerNumber' => '456',
        'color' => 'from-gray-900 to-black',
        'mastery' => 100
    ],
    [
        'id' => 4,
        'name' => 'UI/UX Design',
        'level' => 'ADVANCED',
        'playerNumber' => '303',
        'color' => 'from-gray-900 to-black',
        'mastery' => 85
    ],
    [
        'id' => 5,
        'name' => 'Languages',
        'level' => 'NATIVE',
        'playerNumber' => '404',
        'color' => 'from-gray-900 to-black',
        'mastery' => 100,
        'description' => 'English & Bangla'
    ]
];

// Social links
$socialLinks = [
    [
        'name' => 'Facebook',
        'icon' => '📘',
        'url' => 'https://www.facebook.com/fahim.hasan.santo.2024'
    ],
    [
        'name' => 'Instagram', 
        'icon' => '📷',
        'url' => 'https://www.instagram.com/_fahimsanto/'
    ],
    [
        'name' => 'LinkedIn',
        'icon' => '💼', 
        'url' => 'https://www.linkedin.com/in/fahim-hasan-santo-583987267/'
    ]
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#ff0055">
    <title>FAHIM - Player 456 | Squid Game Portfolio</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon_io/favicon-32x32.png">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Cache Busting for Local Assets -->
    <link rel="stylesheet" href="/css/style.css<?php echo $cacheManager->getCacheBustParam(); ?>">
    <script>
        // Cache version for JavaScript
        window.SQUID_CACHE_VERSION = '<?php echo $cacheManager->getCurrentVersion(); ?>';
        window.SQUID_CACHE_BUST = '<?php echo $cacheManager->getCacheBustParam(); ?>';
    </script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'squid-pink': 'rgb(255, 0, 85)',
                        'squid-green': 'rgb(8, 106, 83)',
                        'squid-red': 'hsl(348, 83%, 47%)'
                    },
                    fontFamily: {
                        'squid': ['Game Of Squids', 'Bebas Neue', 'cursive'],
                        'bebas': ['Game Of Squids', 'Bebas Neue', 'cursive'],
                        'poppins': ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">

    <!-- Particles.js -->
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
</head>
<body class="bg-black text-white overflow-x-hidden">
    <!-- Advanced Preloading System -->
    <div id="squid-preloader" class="squid-preloader">
        <!-- Corner Circles -->
        <div class="corner-circle top-left"></div>
        <div class="corner-circle top-right"></div>
        <div class="corner-circle bottom-left"></div>
        <div class="corner-circle bottom-right"></div>

        <!-- Main Loading Content -->
        <div class="preloader-content">
            <!-- Squid Game Logo -->
            <div class="squid-logo-container">
                <div class="squid-logo">
                    <div class="logo-circle"></div>
                    <div class="logo-triangle"></div>
                    <div class="logo-square"></div>
                </div>
            </div>

            <!-- Loading Text -->
            <div class="loading-text">
                <h2 class="font-squid text-4xl text-squid-pink mb-4 glitch-text">PLAYER 456</h2>
                <p class="text-white text-lg mb-6">INITIALIZING SQUID GAME...</p>

                <!-- Progress Bar -->
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-text">
                        <span id="progress-percentage">0%</span>
                        <span id="progress-status">Loading assets...</span>
                    </div>
                </div>
            </div>

            <!-- Loading Steps -->
            <div class="loading-steps">
                <div class="step" data-step="fonts">
                    <span class="step-icon">○</span>
                    <span class="step-text">Loading fonts</span>
                </div>
                <div class="step" data-step="styles">
                    <span class="step-icon">○</span>
                    <span class="step-text">Loading styles</span>
                </div>
                <div class="step" data-step="scripts">
                    <span class="step-icon">○</span>
                    <span class="step-text">Loading scripts</span>
                </div>
                <div class="step" data-step="assets">
                    <span class="step-icon">○</span>
                    <span class="step-text">Loading assets</span>
                </div>
                <div class="step" data-step="ready">
                    <span class="step-icon">○</span>
                    <span class="step-text">Game ready</span>
                </div>
            </div>
        </div>

        <!-- Haptic Feedback Indicator -->
        <div class="haptic-indicator">
            <div class="haptic-pulse"></div>
            <span class="haptic-text">Haptic feedback enabled</span>
        </div>
    </div>

    <!-- Custom Cursor -->
    <div id="cursor" class="squid-cursor"></div>

    <!-- Scroll Progress -->
    <div id="scroll-progress" class="scroll-progress"></div>

    <!-- Particle Background -->
    <div id="particles-js" style="position:fixed;inset:0;z-index:0;background:transparent;touch-action:none;"></div>
    
    <!-- Audio Elements -->
    <audio id="hover-sound" preload="auto">
        <source src="assets/sounds/hover.mp3" type="audio/mpeg">
        <source src="assets/sounds/hover.wav" type="audio/wav">
    </audio>
    <audio id="nfc-sound" preload="auto">
        <source src="assets/sounds/nfc.mp3" type="audio/mpeg">
        <source src="assets/sounds/nfc.wav" type="audio/wav">
    </audio>
    <audio id="fail-sound" preload="auto">
        <source src="assets/sounds/fail.mp3" type="audio/mpeg">
        <source src="assets/sounds/fail.wav" type="audio/wav">
    </audio>
    
    <!-- Main Content -->
    <div class="min-h-screen relative">
        <!-- Navigation -->
        <nav id="main-nav" class="fixed top-0 left-0 right-0 z-50 p-4 bg-black/50 transition-all duration-500 ease-in-out">
            <div class="nav-container flex justify-between items-center max-w-7xl mx-auto transition-all duration-500 ease-in-out">
                <div class="nav-symbols flex space-x-4 text-squid-pink text-2xl font-bold transition-all duration-500 ease-in-out">
                    <a href="#about" class="hover:animate-glitch cursor-pointer hover:scale-110 transition-transform duration-300">○</a>
                    <a href="#projects" class="hover:animate-glitch cursor-pointer hover:scale-110 transition-transform duration-300">△</a>
                    <a href="#skills" class="hover:animate-glitch cursor-pointer hover:scale-110 transition-transform duration-300">□</a>
                </div>
                
                <!-- Digital Player Number Display -->
                <div class="digital-player-display nav-player-display absolute left-1/2 transform -translate-x-1/2 bg-gradient-to-b from-blue-400 to-blue-600 p-1 rounded-lg shadow-lg border-2 border-blue-300 transition-all duration-500 ease-in-out" style="min-width: 100px;">
                    <div class="bg-black px-4 py-2 rounded-md border border-gray-700 relative overflow-hidden transition-all duration-500 ease-in-out">
                        <div class="absolute inset-0 opacity-10">
                            <div class="grid grid-cols-8 grid-rows-4 h-full w-full">
                                <?php for($i = 0; $i < 32; $i++): ?>
                                <div class="border border-gray-600" style="border-width: 0.5px;"></div>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <div class="font-mono text-2xl font-bold text-red-500 tracking-wider digital-display text-center relative z-10 transition-all duration-500 ease-in-out">456
                        </div>
                    </div>
                </div>
                
                <div class="nav-right flex space-x-4 items-center transition-all duration-500 ease-in-out">
                    <!-- Advanced Digital Clock -->
                    <!-- <div class="digital-clock-container nav-clock hidden sm:block transition-all duration-500 ease-in-out">
                        <div class="digital-clock-frame">
                            <div class="digital-clock-screen">
                                <div class="digital-clock-grid">
                                    <?php for($i = 0; $i < 35; $i++): ?>
                                    <div class="clock-pixel"></div>
                                    <?php endfor; ?>
                                </div>
                                <div class="digital-clock-display">
                                    <div id="digital-time">00:00:00</div>
                                    <div class="clock-date">
                                        <div id="digital-date">MON JAN 01</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> -->

                    <button id="dark-mode-toggle" class="nav-toggle w-12 h-12 bg-squid-green rounded-full flex items-center justify-center hover:bg-squid-pink transition-all duration-500 ease-in-out">
                        🌙
                    </button>
                </div>
            </div>
        </nav>
        
        <!-- Hero Section -->
        <section class="hero-section min-h-screen flex flex-col items-center justify-center text-center px-4 relative">
            <!-- Mobile Guards -->
            <div class="guard-figure-mobile absolute top-20 left-4 md:left-8">
                <div class="guard-body-mobile">
                    <div class="guard-mask-triangular-mobile">
                        <div class="guard-eye-mobile"></div>
                    </div>
                </div>
            </div>
            <div class="guard-figure-mobile absolute top-20 right-4 md:right-8">
                <div class="guard-body-mobile">
                    <div class="guard-mask-round-mobile">
                        <div class="guard-eye-mobile"></div>
                    </div>
                </div>
            </div>

            <!-- Squid Shape -->
            <div class="squid-shape mb-8"></div>

            <!-- Main Content -->
            <div class="text-center mb-8">
                <h1 id="glitch-name" class="font-bold text-4xl sm:text-6xl md:text-8xl lg:text-9xl text-white mb-4 glitch-text relative font-bebas" data-text="<?php echo $currentName; ?>">
                    <span id="name-text"><?php echo $currentName; ?></span>
                </h1>
                <div class="bg-squid-green text-black px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-bold text-lg sm:text-xl md:text-2xl inline-block hover:animate-pulse mb-8">
                    PLAYER 456
                </div>
                
                <!-- Social Links -->
                <div class="flex justify-center space-x-6 mb-8">
                    <a href="#" class="social-link w-12 h-12 bg-squid-pink rounded-full flex items-center justify-center text-white text-xl hover:bg-squid-green transition-colors duration-300">
                           <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.611-3.197-1.559-.748-.948-1.197-2.25-1.197-3.688 0-1.438.449-2.74 1.197-3.688.749-.948 1.9-1.559 3.197-1.559s2.448.611 3.197 1.559c.748.948 1.197 2.25 1.197 3.688 0 1.438-.449 2.74-1.197 3.688-.749.948-1.9 1.559-3.197 1.559zm7.138 0c-1.297 0-2.448-.611-3.197-1.559-.748-.948-1.197-2.25-1.197-3.688 0-1.438.449-2.74 1.197-3.688.749-.948 1.9-1.559 3.197-1.559s2.448.611 3.197 1.559c.748.948 1.197 2.25 1.197 3.688 0 1.438-.449 2.74-1.197 3.688-.749.948-1.9 1.559-3.197 1.559z"/>
                        </svg>
                        
                    </a>
                    <a href="#" class="social-link w-12 h-12 bg-squid-pink rounded-full flex items-center justify-center text-white text-xl hover:bg-squid-green transition-colors duration-300">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                    </a>
                    <a href="#" class="social-link w-12 h-12 bg-squid-pink rounded-full flex items-center justify-center text-white text-xl hover:bg-squid-green transition-colors duration-300">
                     


                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                    </a>
                </div>
                
                <p class="text-squid-green font-medium text-lg animate-float font-poppins">
                    WEB DEVELOPER
                </p>
            </div>
            
            <!-- NFC Detection Zone -->
            <button class="nfc-button nfc-pulse bg-squid-red rounded-full w-16 h-16 flex items-center justify-center mb-8 cursor-pointer hover:animate-bounce mt-8">
                <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center text-squid-red font-bold">
                
                </div>
            </button>
            
            <!-- Scroll Indicator -->
            <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
                <div class="w-6 h-10 border-2 border-white rounded-full flex justify-center">
                    <div class="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
                </div>
            </div>
        </section>

        <!-- About Me Section -->
        <section id="about" class="py-20 px-4 relative">
            <div class="max-w-6xl mx-auto">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <!-- Profile Card -->
                    <div class="relative scroll-reveal-left">
                        <div class="profile-card bg-gradient-to-br from-gray-900 to-black rounded-2xl p-8 border-2 border-squid-pink relative overflow-hidden">
                            <!-- Corner decorations -->
                            <div class="absolute top-4 left-4 w-6 h-6 border-l-2 border-t-2 border-squid-pink"></div>
                            <div class="absolute top-4 right-4 w-6 h-6 border-r-2 border-t-2 border-squid-pink"></div>
                            <div class="absolute bottom-4 left-4 w-6 h-6 border-l-2 border-b-2 border-squid-pink"></div>
                            <div class="absolute bottom-4 right-4 w-6 h-6 border-r-2 border-b-2 border-squid-pink"></div>

                            <!-- Profile Image Placeholder -->
                            <div class="w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-br from-squid-pink to-squid-red flex items-center justify-center text-6xl">
                                <img src="" alt="">
                            </div>

                            <h3 class="text-2xl font-bold text-center text-white mb-2">Fahim Hasan Santo</h3>
                            <p class="text-squid-green text-center mb-4 font-bold">SENIOR WEB DEVELOPER</p>

                            <!-- Contact Info -->
                            <div class="space-y-3 text-sm">
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-400">Email:</span>
                                    <span class="text-white"><EMAIL></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-400">Phone:</span>
                                    <span class="text-white">+8801796680830</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-400">Location:</span>
                                    <span class="text-white">Jashore, Bangladesh</span>
                                </div>
                                <div class="flex items-center justify-between">
                                   
                                </div>
                            </div>

                            <!-- Download CV Button -->
                            <button class="w-full mt-6 bg-squid-green text-black font-bold py-3 rounded-lg hover:bg-squid-pink hover:text-white transition-all duration-300">
                              <a href="/SquidLink-PHP/CV/Fahim Hasan Santo CV.pdf" download="Fahim Hasan Santo CV">Download CV</a>
                            </button>
                        </div>
                    </div>

                    <!-- About Content -->
                    <div class="space-y-6 scroll-reveal-right">
                        <h2 class="text-4xl md:text-5xl font-bold text-white mb-8 squid-reveal">
                            <span class="text-squid-pink">ABOUT</span> ME
                        </h2>

                        <div class="about-content space-y-4 text-gray-300 leading-relaxed">
                            <p>
                                Greetings, I'm <span class="text-squid-pink font-bold">Fahim Hasan Santo</span>, a dedicated and forward-thinking Web Developer with over <span class="text-squid-green font-bold">four years</span> of professional experience in crafting intuitive, scalable, and modern web applications.
                            </p>

                            <p>
                                I am currently pursuing my academic journey at the <span class="text-squid-pink font-bold">American University of International Business (AUIB)</span>, where I specialize in digital systems, development strategies, and emerging web technologies.
                            </p>

                            <p>
                                With a strong foundation in both front-end and back-end development, I have cultivated a versatile skill set that allows me to architect and deploy high-performance digital solutions. My expertise spans across a range of development environments, frameworks, and user experience principles — with a growing focus on <span class="text-squid-green font-bold">NFC-based interaction systems</span>, blending physical experiences with digital technology.
                            </p>
                        </div>

                        <!-- Technical Proficiencies -->
                        <div class="mt-8">
                            <h3 class="text-2xl font-bold text-squid-pink mb-4">Technical Proficiencies</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <h4 class="text-squid-green font-bold mb-2">Programming Languages:</h4>
                                    <p class="text-gray-300">PHP, JavaScript, HTML5, CSS3, SQL</p>
                                </div>
                                <div>
                                    <h4 class="text-squid-green font-bold mb-2">Frameworks & Libraries:</h4>
                                    <p class="text-gray-300">Node.js, Express, Bootstrap, jQuery, React</p>
                                </div>
                                <div>
                                    <h4 class="text-squid-green font-bold mb-2">CMS & Tools:</h4>
                                    <p class="text-gray-300">WordPress, Elementor, WooCommerce, cPanel</p>
                                </div>
                                <div>
                                    <h4 class="text-squid-green font-bold mb-2">Specialties:</h4>
                                    <p class="text-gray-300">E-commerce, NFC solutions, API integration</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Projects Section -->
        <section id="projects" class="py-20 px-4 relative">
            <div class="max-w-7xl mx-auto">
                <h2 class="text-4xl md:text-6xl font-bold text-center mb-16 font-bebas scroll-reveal">
                    <span class="text-squid-pink">MY</span>
                    <span class="text-white">PROJECTS</span>
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                    <?php foreach ($projects as $index => $project): ?>
                    <div class="card-3d h-80 relative cursor-pointer project-card scroll-reveal-stagger"
                         data-url="<?php echo $project['url']; ?>"
                         data-name="<?php echo $project['name']; ?>"
                         style="animation-delay: <?php echo $index * 0.2; ?>s;">
                        <!-- Front Face -->
                        <div class="card-face bg-gradient-to-br <?php echo $project['color']; ?> rounded-xl border-0 p-6 flex flex-col justify-center items-center text-center h-full relative">
                            <div class="absolute top-4 left-4 text-squid-green font-bold text-2xl">
                                <?php echo $project['playerNumber']; ?>
                            </div>
                            <div class="absolute top-4 right-4 text-white font-bold text-xs">
                                ACTIVE
                            </div>
                            <div class="text-white text-2xl font-bold mb-4 font-bebas">
                                <?php echo strtoupper($project['name']); ?>
                            </div>
                            <p class="text-white/90 text-sm mb-6 font-poppins">
                                <?php echo $project['description']; ?>
                            </p>
                            <div class="bg-white/20 text-white px-4 py-2 rounded-lg font-bold text-sm hover:bg-white/30 transition-colors">
                                VIEW PROJECT
                            </div>
                        </div>

                        <!-- Back Face -->
                        <div class="card-face card-back bg-gradient-to-br <?php echo $project['color']; ?> rounded-xl border-0 p-6 flex flex-col justify-center items-center text-center h-full">
                            <div class="text-white text-6xl mb-4">🎯</div>
                            <div class="text-white text-xl font-bold mb-4 font-bebas">
                                MISSION COMPLETE
                            </div>
                            <p class="text-white/90 text-sm font-poppins">
                                Click to visit live project
                            </p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>

        <!-- Player Skills Section -->
        <section id="skills" class="py-20 px-4 relative bg-gray-900/50">
            <div class="max-w-7xl mx-auto">
                <h2 class="text-4xl md:text-6xl font-bold text-center mb-16 font-bebas scroll-reveal">
                    <span class="text-squid-green">PLAYER</span>
                    <span class="text-white">SKILLS</span>
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                    <?php foreach ($playerSkills as $index => $skill): ?>
                    <div class="card-3d h-80 relative cursor-pointer skill-card scroll-reveal-stagger"
                         style="animation-delay: <?php echo $index * 0.2; ?>s;">
                        <!-- Front Face -->
                        <div class="card-face bg-gradient-to-br <?php echo $skill['color']; ?> rounded-xl border-0 p-6 flex flex-col justify-center items-center text-center h-full relative">
                            <div class="absolute top-4 left-4 text-white font-bold text-xs">
                                <?php echo $skill['playerNumber']; ?>
                            </div>
                            <div class="absolute top-4 right-4 text-white font-bold text-xs">
                                <?php echo $skill['level']; ?>
                            </div>
                            <div class="text-white text-2xl font-bold mb-4 font-bebas">
                                <?php echo strtoupper($skill['name']); ?>
                            </div>
                            <?php if (isset($skill['description'])): ?>
                            <p class="text-white/90 text-sm mb-4 font-poppins">
                                <?php echo $skill['description']; ?>
                            </p>
                            <?php endif; ?>

                            <!-- Skill Mastery Bar -->
                            <div class="w-full bg-white/20 rounded-full h-3 mb-4">
                                <div class="bg-white h-3 rounded-full transition-all duration-1000 skill-bar"
                                     data-width="<?php echo $skill['mastery']; ?>%"
                                     style="width: 0%;"></div>
                            </div>
                            <div class="text-white font-bold text-lg">
                                <?php echo $skill['mastery']; ?>% MASTERY
                            </div>
                        </div>

                        <!-- Back Face -->
                        <div class="card-face card-back bg-gradient-to-br <?php echo $skill['color']; ?> rounded-xl border-0 p-6 flex flex-col justify-center items-center text-center h-full">
                            <div class="text-white text-6xl mb-4">⭐</div>
                            <div class="text-white text-xl font-bold mb-4 font-bebas">
                                SKILL UNLOCKED
                            </div>
                            <p class="text-white/90 text-sm font-poppins">
                                Years of experience mastering this technology
                            </p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Call to Action -->
                <div class="bg-gradient-to-r from-squid-pink to-squid-green rounded-2xl p-8 text-center">
                    <h3 class="text-white font-bold text-3xl mb-4 font-bebas">
                        READY FOR YOUR PROJECT?
                    </h3>
                    <p class="text-white/90 text-lg mb-6 font-poppins">
                        Full Stack Web Developer | HTML, CSS, JS, WordPress Master | English & Bangla
                    </p>
                    <div class="flex justify-center space-x-4">
                        <div class="bg-white text-squid-pink px-6 py-3 rounded-lg font-bold cursor-pointer hover:scale-105 transition-transform hover-sound">
                           <a href="https://www.instagram.com/_fahimsanto/">HIRE ME</a> 
                        </div>
                        <div class="bg-transparent border-2 border-white text-white px-6 py-3 rounded-lg font-bold cursor-pointer hover:bg-white hover:text-squid-pink transition-colors hover-sound">
                          <a href="/SquidLink-PHP/CV/Fahim Hasan Santo CV.pdf" download="Fahim Hasan Santo CV"> VIEW CV</a> 
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="py-20 px-4 relative">
            <div class="max-w-4xl mx-auto">
                <!-- Mobile Guards for Contact -->
                <div class="guard-figure absolute top-4 left-4 md:left-8">
                    <div class="guard-body">
                        <div class="guard-mask-triangular">
                            <div class="guard-eye"></div>
                        </div>
                    </div>
                </div>
                <div class="guard-figure absolute top-4 right-4 md:right-8">
                    <div class="guard-body">
                        <div class="guard-mask-round">
                            <div class="guard-eye"></div>
                        </div>
                    </div>
                </div>

                <div class="text-center mb-12 scroll-reveal">
                   <h2 class="text-4xl md:text-6xl font-bold text-center mb-16 font-bebas glitch-reveal">
                    <span class="text-squid-pink">contact</span>
                    <span class="text-white">& Play</span>
                </h2>
                    <p class="text-white/80 text-lg font-poppins">
                        Don't worry, you can still contact me!
                    </p>
                </div>

                <!-- Contact Form -->
                <div class="bg-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-squid-pink/30 relative scroll-reveal-scale">
                    <div id="contact-success" class="hidden bg-squid-green text-black p-4 rounded-lg mb-6 font-bold text-center">
                        Message sent successfully! I'll get back to you soon.
                    </div>
                    <div id="contact-error" class="hidden bg-red-500 text-white p-4 rounded-lg mb-6 font-bold text-center">
                        Failed to send message. Please try again.
                    </div>

                    <form id="contact-form" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-squid-green font-bold mb-2 font-poppins">NAME</label>
                                <input type="text" name="name" required
                                       class="w-full bg-black/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-squid-pink focus:outline-none transition-colors font-poppins"
                                       placeholder="Enter your name">
                                <div class="error-message text-red-400 text-sm mt-1 hidden"></div>
                            </div>
                            <div>
                                <label class="block text-squid-green font-bold mb-2 font-poppins">EMAIL</label>
                                <input type="email" name="email" required
                                       class="w-full bg-black/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-squid-pink focus:outline-none transition-colors font-poppins"
                                       placeholder="Enter your email">
                                <div class="error-message text-red-400 text-sm mt-1 hidden"></div>
                            </div>
                        </div>
                        <div>
                            <label class="block text-squid-green font-bold mb-2 font-poppins">SUBJECT</label>
                            <input type="text" name="subject" required
                                   class="w-full bg-black/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-squid-pink focus:outline-none transition-colors font-poppins"
                                   placeholder="What's this about?">
                            <div class="error-message text-red-400 text-sm mt-1 hidden"></div>
                        </div>
                        <div>
                            <label class="block text-squid-green font-bold mb-2 font-poppins">MESSAGE</label>
                            <textarea name="message" required rows="5"
                                      class="w-full bg-black/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-squid-pink focus:outline-none transition-colors resize-none font-poppins"
                                      placeholder="Tell me about your project..."></textarea>
                            <div class="error-message text-red-400 text-sm mt-1 hidden"></div>
                        </div>
                        <div class="text-center">
                            <button type="submit"
                                    class="bg-gradient-to-r from-squid-pink to-squid-green text-white font-bold py-4 px-8 rounded-lg hover:scale-105 transition-transform hover-sound font-bebas text-xl">
                               Start Game
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </div>

    <!-- Loading Overlay for Project Redirects -->
    <div id="loading-overlay" class="fixed inset-0 bg-black/95 z-50 flex items-center justify-center hidden">
        <div class="text-center">
            <div class="text-6xl mb-4 animate-spin">🦑</div>
            <div class="text-squid-pink text-2xl font-bold mb-4 font-bebas">LOADING GAME...</div>
            <div class="text-white text-lg font-poppins" id="loading-text">Preparing your experience...</div>
            <div class="w-64 bg-gray-700 rounded-full h-2 mt-4">
                <div id="loading-progress" class="bg-gradient-to-r from-squid-pink to-squid-green h-2 rounded-full transition-all duration-100" style="width: 0%;"></div>
            </div>
        </div>
    </div>

    <!-- NFC Animation Overlay -->
    <div id="nfc-overlay" class="fixed inset-0 bg-black/95 z-50 flex items-center justify-center hidden">
        <div class="text-center relative">
            <!-- Red Alert Flash -->
            <div class="absolute inset-0 bg-red-500/30 animate-pulse rounded-full"></div>

            <!-- NFC Card Display -->
            <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 border-4 border-squid-pink shadow-2xl mb-8 transform hover:scale-105 transition-transform">
                <div class="text-8xl mb-4">📱</div>
                <div class="text-squid-pink text-3xl font-bold font-bebas">NFC DETECTED</div>
                <div class="text-white text-lg font-poppins">Card Authentication...</div>
            </div>

            <!-- Player Verification -->
            <div class="bg-squid-green text-black px-8 py-4 rounded-lg font-bold text-2xl mb-8 font-bebas">
                PLAYER 456 VERIFIED
            </div>

            <!-- Corner Guards -->
            <div class="absolute top-4 left-4 guard-figure-small">
                <div class="guard-body-small">
                    <div class="guard-mask-triangular-small">
                        <div class="guard-eye-small"></div>
                    </div>
                </div>
            </div>
            <div class="absolute top-4 right-4 guard-figure-small">
                <div class="guard-body-small">
                    <div class="guard-mask-round-small">
                        <div class="guard-eye-small"></div>
                    </div>
                </div>
            </div>

            <!-- Scanning Effect -->
            <div class="text-white text-lg font-poppins animate-pulse">
                Activating Squid Game Interface...
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="py-12 px-4 bg-black border-t-2 border-squid-pink relative z-10">
        <div class="max-w-7xl mx-auto text-center">
            <div class="flex justify-center items-center space-x-4 mb-8">
                <!-- Geometric shapes -->
                <div class="w-8 h-8 bg-squid-pink rounded-full"></div>
                <div class="w-8 h-8 bg-white"></div>
                <div class="w-8 h-8 bg-squid-green" style="clip-path: polygon(50% 0%, 0% 100%, 100% 100%);"></div>
            </div>
            <p class="text-squid-green font-medium text-lg mb-2">
                © 2025 Fahim ~ Player 456. All games completed successfully.
            </p>
            <p class="text-gray-400 text-sm">
                Designed with Squid Game aesthetics for maximum impact.
            </p>
        </div>
    </footer>

    <?php if ($adminMode): ?>
    <!-- Admin Cache Management Interface -->
    <div id="admin-panel" class="admin-panel">
        <div class="admin-header">
            <h3>🦑 SquidLink Admin Panel</h3>
            <button onclick="toggleAdminPanel()" class="admin-close">×</button>
        </div>
        <div class="admin-content">
            <div class="admin-section">
                <h4>Cache Management</h4>
                <p>Current Version: <span class="version-display"><?php echo $cacheManager->getCurrentVersion(); ?></span></p>
                <div class="admin-buttons">
                    <button onclick="adminClearCache()" class="admin-btn clear">Clear Cache</button>
                    <button onclick="adminRestoreCache()" class="admin-btn restore">Restore Cache</button>
                    <button onclick="adminForceRefresh()" class="admin-btn refresh">Force Refresh</button>
                </div>
            </div>

            <div class="admin-section">
                <h4>Cache Status</h4>
                <div id="cache-status" class="cache-status">
                    <div class="status-item">
                        <span class="status-label">Browser Cache:</span>
                        <span class="status-value" id="browser-cache-status">Checking...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Service Worker:</span>
                        <span class="status-value" id="sw-status">Checking...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">PHP Cache:</span>
                        <span class="status-value" id="php-cache-status">Active</span>
                    </div>
                </div>
            </div>

            <div class="admin-section">
                <h4>Quick Actions</h4>
                <div class="admin-buttons">
                    <button onclick="adminCheckVersion()" class="admin-btn info">Check Version</button>
                    <button onclick="adminViewLogs()" class="admin-btn info">View Logs</button>
                    <button onclick="adminTestHaptic()" class="admin-btn test">Test Haptic</button>
                </div>
            </div>
        </div>
    </div>

    <style>
    .admin-panel {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 350px;
        background: rgba(0, 0, 0, 0.95);
        border: 2px solid rgb(255, 0, 85);
        border-radius: 10px;
        color: white;
        font-family: 'Poppins', sans-serif;
        z-index: 15000;
        box-shadow: 0 10px 30px rgba(255, 0, 85, 0.3);
        backdrop-filter: blur(10px);
    }

    .admin-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        border-bottom: 1px solid rgba(255, 0, 85, 0.3);
    }

    .admin-header h3 {
        margin: 0;
        color: rgb(255, 0, 85);
        font-size: 1.1rem;
    }

    .admin-close {
        background: none;
        border: none;
        color: rgb(255, 0, 85);
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .admin-content {
        padding: 1rem;
        max-height: 70vh;
        overflow-y: auto;
    }

    .admin-section {
        margin-bottom: 1.5rem;
    }

    .admin-section h4 {
        margin: 0 0 0.5rem 0;
        color: rgb(8, 106, 83);
        font-size: 0.9rem;
    }

    .admin-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .admin-btn {
        padding: 0.4rem 0.8rem;
        border: 1px solid;
        background: transparent;
        color: inherit;
        cursor: pointer;
        border-radius: 5px;
        font-size: 0.8rem;
        transition: all 0.3s ease;
    }

    .admin-btn.clear { border-color: #ff4444; color: #ff4444; }
    .admin-btn.clear:hover { background: #ff4444; color: white; }

    .admin-btn.restore { border-color: #44ff44; color: #44ff44; }
    .admin-btn.restore:hover { background: #44ff44; color: black; }

    .admin-btn.refresh { border-color: #ffaa00; color: #ffaa00; }
    .admin-btn.refresh:hover { background: #ffaa00; color: black; }

    .admin-btn.info { border-color: rgb(8, 106, 83); color: rgb(8, 106, 83); }
    .admin-btn.info:hover { background: rgb(8, 106, 83); color: white; }

    .admin-btn.test { border-color: rgb(255, 0, 85); color: rgb(255, 0, 85); }
    .admin-btn.test:hover { background: rgb(255, 0, 85); color: white; }

    .version-display {
        color: rgb(255, 0, 85);
        font-weight: bold;
    }

    .cache-status {
        font-size: 0.8rem;
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.3rem;
    }

    .status-label {
        color: rgba(255, 255, 255, 0.7);
    }

    .status-value {
        color: rgb(8, 106, 83);
    }

    @media (max-width: 768px) {
        .admin-panel {
            width: calc(100% - 40px);
            right: 20px;
            left: 20px;
        }
    }
    </style>

    <script>
    function toggleAdminPanel() {
        const panel = document.getElementById('admin-panel');
        if (panel) {
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }
    }

    async function adminClearCache() {
        if (confirm('Are you sure you want to clear the cache?')) {
            try {
                const response = await fetch('/api/cache?cache_action=clear&key=' + getDailyCacheKey(), {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    alert('Cache cleared successfully! Page will reload.');
                    window.location.reload();
                } else {
                    alert('Failed to clear cache: ' + (result.error || 'Unknown error'));
                }
            } catch (error) {
                alert('Error clearing cache: ' + error.message);
            }
        }
    }

    async function adminRestoreCache() {
        if (confirm('Are you sure you want to restore the previous cache version?')) {
            try {
                const response = await fetch('/api/cache?cache_action=restore&key=' + getDailyCacheKey(), {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    alert('Cache restored successfully! Page will reload.');
                    window.location.reload();
                } else {
                    alert('Failed to restore cache: ' + (result.error || 'No backup available'));
                }
            } catch (error) {
                alert('Error restoring cache: ' + error.message);
            }
        }
    }

    async function adminForceRefresh() {
        if (confirm('This will clear all caches and force a complete refresh. Continue?')) {
            try {
                const response = await fetch('/api/cache?cache_action=force_refresh&key=' + getDailyCacheKey(), {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    alert('Force refresh completed! Page will reload.');
                    window.location.reload(true);
                } else {
                    alert('Failed to force refresh: ' + (result.error || 'Unknown error'));
                }
            } catch (error) {
                alert('Error during force refresh: ' + error.message);
            }
        }
    }

    async function adminCheckVersion() {
        try {
            const response = await fetch('/api/cache?cache_action=version');
            const result = await response.json();

            if (result.success) {
                alert('Current cache version: ' + result.version + '\nTimestamp: ' + new Date(result.timestamp * 1000).toLocaleString());
            } else {
                alert('Failed to check version: ' + (result.error || 'Unknown error'));
            }
        } catch (error) {
            alert('Error checking version: ' + error.message);
        }
    }

    async function adminViewLogs() {
        try {
            const response = await fetch('/api/cache?cache_action=logs&key=' + getDailyCacheKey());
            const result = await response.json();

            if (result.success) {
                const logs = result.logs.slice(0, 10); // Show last 10 entries
                const logText = logs.map(log =>
                    `${log.timestamp} - ${log.event} (${log.version})`
                ).join('\n');

                alert('Recent cache logs:\n\n' + (logText || 'No logs found'));
            } else {
                alert('Failed to view logs: ' + (result.error || 'Unknown error'));
            }
        } catch (error) {
            alert('Error viewing logs: ' + error.message);
        }
    }

    function adminTestHaptic() {
        if (window.squidApp && window.squidApp.triggerHapticFeedback) {
            window.squidApp.triggerHapticFeedback([100, 50, 100, 50, 100]);
            alert('Haptic feedback test sent!');
        } else {
            alert('Haptic feedback not available');
        }
    }

    function getDailyCacheKey() {
        const today = new Date().toISOString().split('T')[0];
        return 'squid456_cache_key_' + today;
    }

    // Update cache status
    function updateCacheStatus() {
        // Check service worker status
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistration().then(registration => {
                const swStatus = document.getElementById('sw-status');
                if (swStatus) {
                    swStatus.textContent = registration ? 'Active' : 'Not registered';
                    swStatus.style.color = registration ? 'rgb(8, 106, 83)' : '#ff4444';
                }
            });
        }

        // Check browser cache
        const browserCacheStatus = document.getElementById('browser-cache-status');
        if (browserCacheStatus) {
            browserCacheStatus.textContent = 'Active';
            browserCacheStatus.style.color = 'rgb(8, 106, 83)';
        }
    }

    // Initialize admin panel
    document.addEventListener('DOMContentLoaded', () => {
        updateCacheStatus();
        setInterval(updateCacheStatus, 30000); // Update every 30 seconds
    });
    </script>
    <?php endif; ?>

    <!-- JavaScript -->
    <script src="js/app.js<?php echo $cacheManager->getCacheBustParam(); ?>"></script>
</body>
</html>
